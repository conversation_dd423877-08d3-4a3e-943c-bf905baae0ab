using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Npgsql;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = "Host=localhost;Port=5432;Database=procuretopaydb;Username=postgres;Password=localdevpassword";

        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            Console.WriteLine("✅ Connected to database successfully!");
            Console.WriteLine();

            // Read and execute the SQL script
            var sqlScript = await File.ReadAllTextAsync("../fix-migration-history.sql");

            // Split the script into individual commands (simple approach)
            var commands = sqlScript.Split(new[] { "-- Step" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var commandSection in commands)
            {
                if (string.IsNullOrWhiteSpace(commandSection)) continue;

                // Extract SQL commands from the section (skip comments)
                var lines = commandSection.Split('\n');
                var sqlCommands = new List<string>();
                var currentCommand = new List<string>();

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("--")) continue;

                    currentCommand.Add(line);

                    if (trimmedLine.EndsWith(";"))
                    {
                        sqlCommands.Add(string.Join("\n", currentCommand));
                        currentCommand.Clear();
                    }
                }

                // Add any remaining command
                if (currentCommand.Count > 0)
                {
                    sqlCommands.Add(string.Join("\n", currentCommand));
                }

                // Execute each SQL command
                foreach (var sqlCommand in sqlCommands)
                {
                    if (string.IsNullOrWhiteSpace(sqlCommand)) continue;

                    try
                    {
                        using var cmd = new NpgsqlCommand(sqlCommand, connection);

                        if (sqlCommand.Trim().ToUpper().StartsWith("SELECT"))
                        {
                            // Handle SELECT queries
                            using var reader = await cmd.ExecuteReaderAsync();

                            // Print column headers
                            if (reader.HasRows)
                            {
                                var columnCount = reader.FieldCount;
                                for (int i = 0; i < columnCount; i++)
                                {
                                    Console.Write($"{reader.GetName(i),-20}");
                                }
                                Console.WriteLine();
                                Console.WriteLine(new string('-', columnCount * 20));

                                // Print rows
                                while (await reader.ReadAsync())
                                {
                                    for (int i = 0; i < columnCount; i++)
                                    {
                                        var value = reader.IsDBNull(i) ? "NULL" : reader.GetValue(i).ToString();
                                        Console.Write($"{value,-20}");
                                    }
                                    Console.WriteLine();
                                }
                            }
                            else
                            {
                                Console.WriteLine("No rows returned.");
                            }
                            Console.WriteLine();
                        }
                        else
                        {
                            // Handle non-SELECT queries
                            var rowsAffected = await cmd.ExecuteNonQueryAsync();
                            if (rowsAffected > 0)
                            {
                                Console.WriteLine($"✅ Command executed successfully. Rows affected: {rowsAffected}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"⚠️  Error executing command: {ex.Message}");
                        Console.WriteLine($"Command: {sqlCommand.Substring(0, Math.Min(100, sqlCommand.Length))}...");
                    }
                }
            }

            Console.WriteLine();
            Console.WriteLine("🎉 Migration history table fix completed!");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            return;
        }
    }
}
